import React, { useEffect, useState, useMemo, useCallback, memo } from "react";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { addToWishlist, getWishlist } from "../store/wishlist/wishlistSlice";
import {
  getImageCategories,
  getImageTypes,
  getAllActiveImages,
} from "../store/image/imageSlice";
import { getAllProducts } from "../store/product/productSlice";
import { PRODUCTS } from "../pages/Product/Product";
import LoadingAnimation from "./Home/home1-jsx/LoadingAnimation";
import { cn } from "../utils/cn";
import { Button } from "../components/ui/Button";
import {
  Heart,
  ArrowUp,
  Download,
  X,
  Check,
  ShoppingBag,
  ChevronLeft,
  Search,
  Shuffle,
  ImageIcon,
  Filter,
} from "lucide-react";

// Memoized components for better performance
const ImageCard = memo(
  ({ image, favoriteImageIds, onImageClick, onToggleFavorite }) => (
    <div
      className="group glass-card overflow-hidden hover-scale cursor-pointer"
      onClick={() => onImageClick(image)}
    >
      <div className="relative aspect-square overflow-hidden">
        {/* Background pattern for transparent images */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-800 dark:via-gray-700 dark:to-gray-800">
          <div className="absolute inset-0 opacity-30 bg-[radial-gradient(circle_at_1px_1px,rgba(0,0,0,0.1)_1px,transparent_0)] bg-[length:20px_20px] dark:bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.1)_1px,transparent_0)]"></div>
        </div>
        <img
          src={image.image[0]}
          alt={image.title || "Shop Item"}
          className="relative z-10 w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-500"
          loading="lazy"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20" />
      </div>

      <button
        className="absolute top-1 right-1 sm:top-3 sm:right-3 p-1 sm:p-2 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm transition-all duration-200 hover:bg-white dark:hover:bg-gray-700 z-10"
        onClick={(e) => {
          e.stopPropagation();
          onToggleFavorite(image._id);
        }}
      >
        <Heart
          className={cn(
            "w-3 h-3 sm:w-5 sm:h-5 transition-colors",
            favoriteImageIds.includes(image._id)
              ? "text-red-500 fill-red-500"
              : "text-gray-500 hover:text-red-500"
          )}
        />
      </button>
    </div>
  )
);

ImageCard.displayName = "ImageCard";

const FilterButton = memo(({ type, isSelected, onClick, children }) => (
  <button
    onClick={onClick}
    className={cn(
      "px-4 py-2 rounded-full transition-colors flex items-center space-x-2 whitespace-nowrap flex-shrink-0",
      isSelected
        ? "bg-primary text-white"
        : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
    )}
  >
    {children}
  </button>
));

FilterButton.displayName = "FilterButton";

const Shop = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedImage, setSelectedImage] = useState(null);
  const [favoriteImageIds, setFavoriteImageIds] = useState([]);
  const [selectedType, setSelectedType] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [shuffledImages, setShuffledImages] = useState([]);
  const [showProductModal, setShowProductModal] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [showMobileFilters, setShowMobileFilters] = useState(false);

  const { imageCategories, imageTypes, activeImages, loading, error } =
    useSelector((state) => state.image);
  const { wishlist } = useSelector((state) => state.wishlist);
  const { products } = useSelector((state) => state.product);

  // Memoized scroll handler
  const handleScroll = useCallback(() => {
    if (window.scrollY > 300) {
      setShowScrollTop(true);
    } else {
      setShowScrollTop(false);
    }
  }, []);

  useEffect(() => {
    dispatch(getImageTypes());
    dispatch(getImageCategories());
    dispatch(getAllActiveImages());
    dispatch(getAllProducts());
    dispatch(getWishlist());

    // Setup scroll event listener for the scroll-to-top button
    window.addEventListener("scroll", handleScroll, { passive: true });

    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 1000);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      clearTimeout(timer);
    };
  }, [dispatch, handleScroll]);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Memoized scroll to top function
  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  // Memoized shuffle function
  const shuffleArray = useCallback((array) => {
    if (!array) return [];
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }, []);

  useEffect(() => {
    if (activeImages?.length) {
      setShuffledImages(shuffleArray(activeImages));
    }
  }, [activeImages, shuffleArray]);

  useEffect(() => {
    if (wishlist?.image) {
      const wishlistIds = wishlist.image.map((item) => item._id);
      setFavoriteImageIds(wishlistIds);
    }
  }, [wishlist]);

  // Memoized reshuffle handler
  const handleReshuffle = useCallback(() => {
    const newShuffledImages = shuffleArray([...activeImages]);
    setShuffledImages(newShuffledImages);
  }, [activeImages, shuffleArray]);

  // Memoized filtered images
  const filteredImages = useMemo(() => {
    return shuffledImages?.filter((img) => {
      const matchesSearch =
        !searchQuery ||
        (img.title &&
          img.title.toLowerCase().includes(searchQuery.toLowerCase()));
      const matchesType =
        !selectedType ||
        (img.image_type &&
          img.image_type.some((type) =>
            typeof type === "string"
              ? type === selectedType
              : type._id === selectedType
          ));
      const matchesCategory =
        !selectedCategory ||
        (img.image_category &&
          img.image_category.some((category) =>
            typeof category === "string"
              ? category === selectedCategory
              : category._id === selectedCategory
          ));
      const isNotWishlisted = !favoriteImageIds.includes(img._id);
      return matchesSearch && matchesType && matchesCategory && isNotWishlisted;
    });
  }, [
    shuffledImages,
    selectedType,
    selectedCategory,
    searchQuery,
    favoriteImageIds,
  ]);

  // Memoized type select handler
  const handleTypeSelect = useCallback(
    (typeId) => {
      setSelectedType(typeId === selectedType ? null : typeId);
    },
    [selectedType]
  );

  // Memoized category select handler
  const handleCategorySelect = useCallback(
    (categoryId) => {
      setSelectedCategory(categoryId === selectedCategory ? null : categoryId);
    },
    [selectedCategory]
  );

  // Memoized toggle favorite handler
  const toggleFavorite = useCallback(
    (imageId) => {
      dispatch(addToWishlist({ prodId: imageId })).then(() => {
        dispatch(getWishlist());
      });
    },
    [dispatch]
  );

  // Memoized download handler
  const handleDownload = useCallback(async (imageUrl) => {
    try {
      console.log("🔽 Starting download for:", imageUrl);

      // Check if this is an OBS image
      const isOBSImage = imageUrl.includes('obsv3.et-global-1.ethiotelecom.et');

      if (isOBSImage) {
        console.log("📦 OBS image detected, using backend download endpoint");
        // Use backend download endpoint for OBS images
        const downloadUrl = `/api/v1/images/download?url=${encodeURIComponent(imageUrl)}`;

        // Create a temporary link to trigger download
        const link = document.createElement("a");
        link.href = downloadUrl;
        const filename = imageUrl.split("/").pop() || "image.jpg";
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        link.parentNode.removeChild(link);

        console.log("✅ OBS image download initiated");
      } else {
        console.log("☁️ Cloudinary image detected, using direct download");
        // For Cloudinary images, try direct download first
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        const filename = imageUrl.split("/").pop() || "image.jpg";
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        link.parentNode.removeChild(link);
        window.URL.revokeObjectURL(url);

        console.log("✅ Cloudinary image download completed");
      }
    } catch (error) {
      console.error("❌ Error downloading image:", error);

      // Fallback: try using backend endpoint for any image
      try {
        console.log("🔄 Trying fallback download via backend...");
        const downloadUrl = `/api/v1/images/download?url=${encodeURIComponent(imageUrl)}`;
        const link = document.createElement("a");
        link.href = downloadUrl;
        const filename = imageUrl.split("/").pop() || "image.jpg";
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        link.parentNode.removeChild(link);
        console.log("✅ Fallback download initiated");
      } catch (fallbackError) {
        console.error("❌ Fallback download also failed:", fallbackError);
        alert("Failed to download image. Please try again later.");
      }
    }
  }, []);

  // Memoized use image handler
  const handleUseImage = useCallback(() => {
    setShowProductModal(true);
  }, []);

  // Memoized product select handler
  const handleProductSelect = useCallback(
    (product) => {
      if (location.state?.fromProduct) {
        // If coming from product page, go back to the product with the selected image
        navigate(`/products-details/${location.state.productId}`, {
          state: {
            selectedImageUrl: selectedImage.image[0],
            selectedImageId: selectedImage._id,
            uploaderId: selectedImage.uploader, // Include uploader ID if it exists
            product: location.state.product,
            tshirtFacing: location.state?.tshirtFacing, // Pass back the tshirt facing state
          },
        });
      } else {
        // Original product selection logic
        navigate(`/products-details/${product._id}`, {
          state: {
            selectedImageUrl: selectedImage.image[0],
            selectedImageId: selectedImage._id,
            uploaderId: selectedImage.uploader, // Include uploader ID if it exists
            product: product,
          },
        });
      }
      setShowProductModal(false);
      setSelectedImage(null);
    },
    [location.state, selectedImage, navigate]
  );

  // Memoized filtered categories
  const filteredCategories = useMemo(() => {
    if (!selectedType) return [];
    return imageCategories?.filter(
      (category) => category.image_type._id === selectedType
    );
  }, [selectedType, imageCategories]);

  // Memoized image click handler
  const handleImageClick = useCallback((image) => {
    setSelectedImage(image);
  }, []);

  // Memoized clear filters handler
  const handleClearFilters = useCallback(() => {
    setSearchQuery("");
    setSelectedType(null);
    setSelectedCategory(null);
  }, []);

  // Memoized mobile filters toggle
  const toggleMobileFilters = useCallback(() => {
    setShowMobileFilters(!showMobileFilters);
  }, [showMobileFilters]);

  // Error handling
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#fdfcfa] dark:bg-gray-900">
        <div className="glass-card p-10 text-center max-w-2xl">
          <div className="text-red-500 text-7xl mb-6">⚠️</div>
          <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-6">
            Something went wrong
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            {error}
          </p>
          <Button
            onClick={() => {
              dispatch(getImageTypes());
              dispatch(getImageCategories());
              dispatch(getAllActiveImages());
            }}
            className="bg-primary hover:bg-primary/90 rounded-full"
            size="lg"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full bg-[#fdfcfa] dark:bg-gray-900 transition-colors duration-300">
      {/* Loading Screen */}
      {pageLoading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 transition-opacity duration-500">
          <div className="text-center">
            <LoadingAnimation size="lg" className="mx-auto mb-6" />
            <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-pink-500 animate-pulse-slow">
              OnPrintZ
            </div>
          </div>
        </div>
      )}

      <main
        className={cn(
          "transition-opacity duration-500",
          pageLoading ? "opacity-0" : "opacity-100"
        )}
      >
        {/* Hero Section */}
        <section className="relative pt-4 pb-20 overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 -z-10 overflow-hidden">
            <div className="absolute top-0 left-1/4 w-1/3 h-1/3 bg-gradient-to-br from-primary/30 to-accent/30 blur-[120px] dark:from-primary/20 dark:to-accent/20" />
            <div className="absolute bottom-0 right-1/4 w-1/3 h-1/3 bg-gradient-to-tl from-accent/20 to-primary/20 blur-[120px] dark:from-accent/10 dark:to-primary/10" />
          </div>

          <div className="max-w-7xl mx-auto px-6 md:px-12">
            {location.state?.fromProduct && (
              <button
                onClick={() => {
                  // Navigate back to product with the fromShopOrFavorites flag
                  if (location.state?.productId) {
                    navigate(`/products-details/${location.state.productId}`, {
                      state: {
                        fromShopOrFavorites: true,
                        product: location.state.product,
                        tshirtFacing: location.state?.tshirtFacing,
                      },
                    });
                  } else {
                    navigate(-1);
                  }
                }}
                className="mb-8 px-4 py-2 glass-card dark:bg-gray-800 text-gray-700 dark:text-gray-300
                  rounded-full hover:bg-gray-200/50 dark:hover:bg-gray-800/50 transition-all duration-200
                  flex items-center gap-2"
              >
                <ChevronLeft className="w-5 h-5 text-teal-500" />
                Back to Product
              </button>
            )}

            <div className="text-center max-w-3xl mx-auto">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                Image <span className="text-gradient-accent">Shop</span>
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-2">
                Find the perfect image for your custom print-on-demand products
              </p>
            </div>
          </div>
        </section>

        {/* Content Section */}
        <section className="py-2">
          <div className="max-w-7xl mx-auto px-6 md:px-12">
            {/* Desktop Filters */}
            <div className="hidden xl:flex flex-col xl:flex-row gap-8 mb-8">
              <Button
                onClick={handleReshuffle}
                className="w-full md:w-auto bg-primary hover:bg-primary/90 rounded-full"
                size="lg"
              >
                <Shuffle className="mr-2 h-4 w-4" />
                Shuffle Images
              </Button>

              {/* Desktop Filters Section */}
              <div className="flex-1">
                {/* Image Types Tabs */}
                <div className="mb-4 overflow-x-auto scrollbar-hide">
                  <div className="flex flex-wrap xl:flex-nowrap gap-2 pb-1">
                    {imageTypes?.map((type) => (
                      <FilterButton
                        key={type._id}
                        type={type._id}
                        isSelected={selectedType === type._id}
                        onClick={() => {
                          handleTypeSelect(type._id);
                          setSelectedCategory(null); // Reset category when changing type
                        }}
                      >
                        <ImageIcon className="w-4 h-4 mr-1" />
                        <span>{type.image_type}</span>
                      </FilterButton>
                    ))}
                  </div>
                </div>

                {/* Categories - Only show if type is selected */}
                {selectedType && filteredCategories?.length > 0 && (
                  <div className="overflow-x-auto scrollbar-hide">
                    <div className="flex flex-wrap xl:flex-nowrap gap-2 pb-1">
                      {filteredCategories?.map((category) => (
                        <FilterButton
                          key={category._id}
                          type={category._id}
                          isSelected={selectedCategory === category._id}
                          onClick={() => handleCategorySelect(category._id)}
                        >
                          <Filter className="w-4 h-4 mr-1" />
                          <span>{category.image_category}</span>
                        </FilterButton>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Mobile/Tablet/Large Filters */}
            <div className="xl:hidden mb-6">
              <div className="flex gap-3 mb-4">
                <Button
                  onClick={handleReshuffle}
                  className="flex-1 bg-primary hover:bg-primary/90 rounded-full"
                  size="sm"
                >
                  <Shuffle className="mr-2 h-4 w-4" />
                  Shuffle
                </Button>

                <Button
                  onClick={toggleMobileFilters}
                  variant="outline"
                  className="flex-1 rounded-full border-primary text-primary hover:bg-primary hover:text-white"
                  size="sm"
                >
                  <Filter className="mr-2 h-4 w-4" />
                  Filters
                  {(selectedType || selectedCategory) && (
                    <span className="ml-2 bg-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {(selectedType ? 1 : 0) + (selectedCategory ? 1 : 0)}
                    </span>
                  )}
                </Button>
              </div>

              {/* Mobile Filter Panel */}
              {showMobileFilters && (
                <div className="glass-card p-4 rounded-xl space-y-4 animate-in slide-in-from-top-2 duration-300">
                  {/* Image Types */}
                  <div>
                    <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                      Image Types
                    </h3>
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                      {imageTypes?.map((type) => (
                        <button
                          key={type._id}
                          onClick={() => {
                            handleTypeSelect(type._id);
                            setSelectedCategory(null);
                          }}
                          className={cn(
                            "px-3 py-2 rounded-lg text-sm font-medium transition-colors text-center",
                            selectedType === type._id
                              ? "bg-primary text-white"
                              : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                          )}
                        >
                          {type.image_type}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Categories */}
                  {selectedType && filteredCategories?.length > 0 && (
                    <div>
                      <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                        Categories
                      </h3>
                      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                        {filteredCategories?.map((category) => (
                          <button
                            key={category._id}
                            onClick={() => handleCategorySelect(category._id)}
                            className={cn(
                              "px-3 py-2 rounded-lg text-sm font-medium transition-colors text-center",
                              selectedCategory === category._id
                                ? "bg-accent text-white"
                                : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                            )}
                          >
                            {category.image_category}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Clear Filters */}
                  {(selectedType || selectedCategory) && (
                    <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                      <Button
                        onClick={handleClearFilters}
                        variant="outline"
                        className="w-full rounded-lg"
                        size="sm"
                      >
                        Clear All Filters
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Image Grid */}
            <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2 sm:gap-6">
              {filteredImages?.map((image) => (
                <ImageCard
                  key={image._id}
                  image={image}
                  favoriteImageIds={favoriteImageIds}
                  onImageClick={handleImageClick}
                  onToggleFavorite={toggleFavorite}
                />
              ))}
            </div>

            {/* No Results Message */}
            {filteredImages?.length === 0 && (
              <div className="text-center py-12">
                <ImageIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">No images found</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Try adjusting your search or filters to find what you're
                  looking for.
                </p>
                <Button
                  onClick={handleClearFilters}
                  variant="outline"
                  className="rounded-full"
                >
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        </section>
      </main>

      {/* Image Preview Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black/75 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="glass-card p-0 rounded-2xl max-w-[90%] max-h-[90vh] overflow-hidden relative w-[800px]">
            <div className="relative">
              {/* Background pattern for transparent images in modal */}
              <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-800 dark:via-gray-700 dark:to-gray-800 -z-10">
                <div className="absolute inset-0 opacity-20 bg-[radial-gradient(circle_at_2px_2px,rgba(0,0,0,0.1)_2px,transparent_0)] bg-[length:40px_40px] dark:bg-[radial-gradient(circle_at_2px_2px,rgba(255,255,255,0.1)_2px,transparent_0)]"></div>
              </div>
              <img
                src={selectedImage.image[0]}
                alt={selectedImage.title || "Selected Image"}
                className="w-full h-auto max-h-[70vh] object-contain"
              />

              <div className="absolute top-4 right-4 flex gap-2 z-20">
                <button
                  onClick={() => handleDownload(selectedImage.image[0])}
                  className="bg-white/90 hover:bg-green-500 text-green-600 hover:text-white p-3 rounded-full transition-colors duration-300 shadow-md"
                  title="Download Image"
                >
                  <Download className="h-5 w-5" />
                </button>

                <button
                  onClick={() => toggleFavorite(selectedImage._id)}
                  className={cn(
                    "p-3 rounded-full transition-colors duration-300 shadow-md",
                    favoriteImageIds.includes(selectedImage._id)
                      ? "bg-red-500 text-white"
                      : "bg-white/90 text-red-500 hover:bg-red-500 hover:text-white"
                  )}
                  title={
                    favoriteImageIds.includes(selectedImage._id)
                      ? "Remove from Favorites"
                      : "Add to Favorites"
                  }
                >
                  <Heart
                    className={cn(
                      "h-5 w-5",
                      favoriteImageIds.includes(selectedImage._id) &&
                        "fill-white"
                    )}
                  />
                </button>

                <button
                  className="bg-white/90 hover:bg-gray-700 text-gray-700 hover:text-white p-3 rounded-full transition-colors duration-300 shadow-md"
                  onClick={() => setSelectedImage(null)}
                  title="Close"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>

            <div className="p-6 bg-white/80 dark:bg-gray-800/80 backdrop-blur-md">
              <h3 className="text-2xl font-semibold text-gray-800 dark:text-white mb-4">
                {selectedImage.title || "Image Details"}
              </h3>

              <div className="flex flex-wrap gap-4 mt-4">
                <Button
                  onClick={
                    location.state?.fromProduct
                      ? () => handleProductSelect(location.state.product)
                      : handleUseImage
                  }
                  className="flex-1 bg-primary hover:bg-primary/90 rounded-full"
                  size="lg"
                >
                  <Check className="mr-2 h-4 w-4" />
                  {location.state?.fromProduct
                    ? "Select This Image"
                    : "Use This Image"}
                </Button>

                <Button
                  onClick={() => setSelectedImage(null)}
                  variant="outline"
                  className="rounded-full"
                  size="lg"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Product Selection Modal */}
      {showProductModal && !location.state?.fromProduct && (
        <div className="fixed inset-0 bg-black/75 backdrop-blur-sm flex items-center justify-center p-4 z-[60]">
          <div className="glass-card p-8 rounded-2xl max-w-5xl max-h-[90vh] overflow-y-auto relative">
            <div className="text-center mb-8">
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-primary/10 dark:bg-primary/20 mb-6">
                <ShoppingBag className="h-8 w-8 text-primary" />
              </div>
              <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">
                Select Product Type
              </h2>
              <p className="text-gray-600 dark:text-gray-400 max-w-xl mx-auto">
                Choose a product to use with your selected image
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <button
                  key={product.id}
                  onClick={() => handleProductSelect(product)}
                  className="group glass-card overflow-hidden hover-scale"
                >
                  <div className="relative aspect-square overflow-hidden">
                    <img
                      src={product.imageFront}
                      alt={product.name}
                      className="w-full h-full object-contain transform group-hover:scale-105 transition-transform duration-700"
                      loading="lazy"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <span className="text-white font-medium px-4 py-2 bg-primary/80 rounded-lg transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                        Select
                      </span>
                    </div>
                  </div>
                  <div className="p-4 text-center">
                    <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                      {product.name}
                    </h3>
                  </div>
                </button>
              ))}
            </div>

            <div className="mt-8 text-center">
              <Button
                onClick={() => setShowProductModal(false)}
                variant="outline"
                className="rounded-full"
                size="lg"
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Scroll to top button */}
      <button
        onClick={scrollToTop}
        className={cn(
          "fixed bottom-8 right-8 z-50 p-3 rounded-full bg-primary text-white shadow-lg transition-all duration-300 hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-900",
          showScrollTop
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-10 pointer-events-none"
        )}
        aria-label="Scroll to top"
      >
        <ArrowUp className="h-5 w-5" />
      </button>
    </div>
  );
};

export default memo(Shop);
