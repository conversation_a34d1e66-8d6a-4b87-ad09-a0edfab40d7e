const express = require("express");
const router = express.Router();
const {
  createImage,
  getAllImages,
  deleteImage,
  updateImage,
  uploadImage,
  updateImageStatus,
  getAllActiveImages,
  bulkDeleteImages,
  proxyOBSImage,
} = require("../../controllers/image/imageCtrl");
const {
  authOrAdminMiddleware,
  adminAuthMiddleware,
} = require("../../middlewares/authMiddleware");
const {
  securityVerificationMiddleware,
} = require("../../middlewares/securityMiddleware");

router.post(
  "/create-image",
  authOrAdminMiddleware,
  securityVerificationMiddleware("create"),
  createImage
);
router.post(
  "/upload",
  authOrAdminMiddleware,
  securityVerificationMiddleware("create"),
  uploadImage
);
router.get("/all-images", getAllImages);
router.get("/active-images", getAllActiveImages);
router.put(
  "/:id",
  authOrAdminMiddleware,
  securityVerificationMiddleware("edit"),
  updateImage
);
router.put(
  "/status/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  updateImageStatus
);
router.delete(
  "/delete/:id",
  authOrAdminMiddleware,
  securityVerificationMiddleware("delete"),
  deleteImage
);
router.post(
  "/bulk-delete",
  adminAuthMiddleware,
  securityVerificationMiddleware("delete"),
  bulkDeleteImages
);

// OBS proxy endpoint for CORS issues - no auth required for public access
router.get("/obs-proxy/:key", proxyOBSImage);

module.exports = router;
