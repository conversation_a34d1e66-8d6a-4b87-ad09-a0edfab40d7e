const Image = require("../../models/image/imageModel");
const validateMongoDbId = require("../../utils/validateMongoDbId");
const asyncHandler = require("express-async-handler");
const formidable = require("formidable");
const path = require("path");
const obsService = require("../../services/obsService");
const imageCacheService = require("../../services/imageCacheService");

const createImage = asyncHandler(async (req, res) => {
  try {
    const { image, image_category, image_type } = req.body;

    // Validate MongoDB IDs
    validateMongoDbId(image_category);
    validateMongoDbId(image_type);

    const newImage = await Image.create({
      image,
      image_category,
      image_type,
    });

    // Invalidate image caches after creating new image
    await imageCacheService.invalidateImageCache(newImage._id, newImage);

    res.status(201).json(newImage);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

const getAllImages = asyncHandler(async (req, res) => {
  try {
    // Try to get from cache first
    const cachedImages = await imageCacheService.cacheAllImages();

    if (cachedImages && cachedImages.images) {
      console.log("🎯 Serving all images from cache");
      return res.status(200).json(cachedImages.images);
    }

    // Fallback to database if cache fails
    console.log("⚠️ Cache miss, fetching all images from database");
    const images = await Image.find();
    res.status(200).json(images);
  } catch (error) {
    console.error("Error in getAllImages:", error);
    // Fallback to database on any error
    const images = await Image.find();
    res.status(200).json(images);
  }
});

const getAllActiveImages = asyncHandler(async (req, res) => {
  try {
    // Try to get from cache first
    const cachedImages = await imageCacheService.cacheAllActiveImages();

    if (cachedImages && cachedImages.images) {
      console.log("🎯 Serving active images from cache");
      return res.status(200).json(cachedImages.images);
    }

    // Fallback to database if cache fails
    console.log("⚠️ Cache miss, fetching active images from database");
    const images = await Image.find({ status: "active" });
    res.status(200).json(images);
  } catch (error) {
    console.error("Error in getAllActiveImages:", error);
    // Fallback to database on any error
    const images = await Image.find({ status: "active" });
    res.status(200).json(images);
  }
});

const updateImage = asyncHandler(async (req, res) => {
  const { id } = req.params;
  validateMongoDbId(id);
  try {
    // Log user role for debugging
    console.log("User role in updateImage:", req.user.role);

    // Check if user is an administrator
    const isAdmin = req.user.role === "administrator";

    // If not admin, set status to pending
    const updatedData = isAdmin ? req.body : { ...req.body, status: "pending" };

    // If status is being changed to rejected, ensure rejectionReason is provided and set rejectedAt
    if (updatedData.status === "rejected") {
      if (!updatedData.rejectionReason) {
        return res.status(400).json({
          success: false,
          message: "Rejection reason is required when rejecting an image",
        });
      }
      updatedData.rejectedAt = new Date();
    }

    console.log("Is admin:", isAdmin);
    console.log("Updated data:", updatedData);

    const updatedImage = await Image.findByIdAndUpdate(id, updatedData, {
      new: true,
    });

    // Invalidate image caches after update
    await imageCacheService.invalidateImageCache(id, updatedImage);

    res.status(200).json(updatedImage);
  } catch (error) {
    throw new Error(error);
  }
});

const deleteImage = asyncHandler(async (req, res) => {
  const { id } = req.params;
  validateMongoDbId(id);
  try {
    // First find the image to get its URL
    const image = await Image.findById(id);

    if (!image) {
      return res.status(404).json({ error: "Image not found" });
    }

    // Delete image from OBS if it exists
    if (image.image && image.image.length > 0) {
      for (const imageUrl of image.image) {
        try {
          const result = await obsService.deleteImageByUrl(imageUrl);
          console.log(`Deleted image from OBS: ${imageUrl}`, result);
        } catch (obsError) {
          console.error(
            `Error deleting image from OBS: ${imageUrl}`,
            obsError
          );
          // Continue with deletion even if OBS deletion fails
        }
      }
    }

    // Now delete the image from the database
    const deletedImage = await Image.findByIdAndDelete(id);

    // Invalidate image caches after deletion
    await imageCacheService.invalidateImageCache(id, image);

    res.status(200).json(deletedImage);
  } catch (error) {
    console.error("Error in deleteImage:", error);
    res.status(500).json({ error: error.message });
  }
});

const uploadImage = asyncHandler(async (req, res) => {
  const form = new formidable.IncomingForm();
  form.multiples = true; // Allow multiple file uploads

  form.parse(req, async (err, fields, files) => {
    if (err) {
      return res.status(400).json({ error: "File parsing error." });
    }

    const images = Array.isArray(files.image) ? files.image : [files.image];

    if (!images.length) {
      return res.status(400).json({ error: "No files uploaded." });
    }

    try {
      const uploadedImages = await Promise.all(
        images.map(async (image) => {
          const fileName = path.basename(image.originalFilename || image.name || 'image.jpg');
          const result = await obsService.uploadImage(image.filepath, fileName, {
            folder: "images",
            metadata: {
              'x-obs-meta-upload-source': 'image-upload',
              'x-obs-meta-uploader': req.user._id.toString()
            }
          });

          if (result) {
            fields.image_categories.forEach((categoryId) => {
              validateMongoDbId(categoryId);
            });

            fields.image_types.forEach((typeId) => {
              validateMongoDbId(typeId);
            });

            const newImageData = {
              image: [result.url],
              image_category: fields.image_categories,
              image_type: fields.image_types,
            };

            // Log user role for debugging
            console.log("User role in uploadImage:", req.user.role);

            // Check if user is an administrator
            const isAdmin = req.user.role === "administrator";
            console.log("Is admin in uploadImage:", isAdmin);

            // Only store uploader ID if the user is not an admin
            if (!isAdmin) {
              newImageData.uploader = req.user._id;
            }
            newImageData.status = "pending";

            return await Image.create(newImageData);
          } else {
            throw new Error("Image upload failed");
          }
        })
      );

      // Invalidate image caches after upload
      await imageCacheService.invalidateAllImageCaches();

      res.status(201).json(uploadedImages);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
});

const updateImageStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  validateMongoDbId(id);

  try {
    const updatedImage = await Image.findByIdAndUpdate(
      id,
      { status },
      { new: true }
    );

    if (!updatedImage) {
      return res.status(404).json({ error: "Image not found" });
    }

    // Invalidate image caches after status update
    await imageCacheService.invalidateImageCache(id, updatedImage);

    res.status(200).json(updatedImage);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Bulk delete images (by filter or IDs)
const bulkDeleteImages = asyncHandler(async (req, res) => {
  const { ids, status, startDate, endDate } = req.body;
  let filter = {};

  if (ids && Array.isArray(ids) && ids.length > 0) {
    filter._id = { $in: ids };
  } else {
    if (status) filter.status = status;
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = new Date(startDate);
      if (endDate) filter.createdAt.$lte = new Date(endDate);
    }
  }

  const images = await Image.find(filter);
  if (!images.length) {
    return res
      .status(404)
      .json({ success: false, message: "No images found for deletion." });
  }

  let successCount = 0;
  let failCount = 0;
  let errors = [];

  for (const image of images) {
    try {
      if (image.image && image.image.length > 0) {
        for (const imageUrl of image.image) {
          try {
            await obsService.deleteImageByUrl(imageUrl);
          } catch (obsErr) {
            errors.push({ id: image._id, imageUrl, error: obsErr.message });
          }
        }
      }
      await Image.findByIdAndDelete(image._id);
      successCount++;
    } catch (err) {
      failCount++;
      errors.push({ id: image._id, error: err.message });
    }
  }

  // Invalidate all image caches after bulk deletion
  await imageCacheService.invalidateAllImageCaches();

  res.json({
    success: true,
    deleted: successCount,
    failed: failCount,
    errors,
  });
});

/**
 * Download image (handles both Cloudinary and OBS)
 * @desc Download image with proper headers for browser download
 * @route GET /api/v1/images/download
 * @access Public
 */
const downloadImage = asyncHandler(async (req, res) => {
  try {
    const { url } = req.query;

    if (!url) {
      return res.status(400).json({ error: "Image URL is required" });
    }

    console.log("🔽 Download request for:", url);

    // Check if this is an OBS image
    if (obsService.isOBSUrl(url)) {
      console.log("📦 OBS image detected, using OBS service");

      // Extract object key from OBS URL
      const urlParts = url.split('/');
      const bucketIndex = urlParts.findIndex(part => part.includes('obsv3.et-global-1.ethiotelecom.et'));

      if (bucketIndex === -1 || bucketIndex + 2 >= urlParts.length) {
        return res.status(400).json({ error: "Invalid OBS URL format" });
      }

      const objectKey = urlParts.slice(bucketIndex + 2).join('/');
      console.log("🔑 Extracted object key:", objectKey);

      // Download from OBS
      const imageBuffer = await obsService.downloadImage(objectKey);
      const metadata = await obsService.getImageMetadata(objectKey);

      // Extract filename from object key
      const filename = objectKey.split('/').pop() || 'image.jpg';

      // Set download headers
      res.set({
        'Content-Type': metadata.contentType || 'image/jpeg',
        'Content-Length': imageBuffer.length,
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-cache'
      });

      res.send(imageBuffer);

    } else {
      console.log("☁️ Cloudinary image detected, proxying request");

      // For Cloudinary images, proxy the request
      const https = require('https');
      const http = require('http');

      const urlObj = new URL(url);
      const httpModule = urlObj.protocol === 'https:' ? https : http;

      const request = httpModule.get(url, (response) => {
        // Extract filename from URL
        const filename = url.split('/').pop() || 'image.jpg';

        // Set download headers
        res.set({
          'Content-Type': response.headers['content-type'] || 'image/jpeg',
          'Content-Length': response.headers['content-length'],
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Cache-Control': 'no-cache'
        });

        // Pipe the response
        response.pipe(res);
      });

      request.on('error', (error) => {
        console.error("❌ Error downloading Cloudinary image:", error);
        res.status(500).json({ error: "Failed to download image" });
      });
    }

  } catch (error) {
    console.error("❌ Error in downloadImage:", error);
    res.status(500).json({
      error: `Failed to download image: ${error.message}`
    });
  }
});

module.exports = {
  createImage,
  getAllImages,
  getAllActiveImages,
  updateImage,
  deleteImage,
  uploadImage,
  updateImageStatus,
  bulkDeleteImages,
  downloadImage,
};
