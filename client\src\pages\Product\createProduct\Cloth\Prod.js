import React, { useState, useLayoutEffect, useEffect, useRef } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import { fabric } from "fabric";
import { useDispatch, useSelector } from "react-redux";
import { ProductSelector } from "../../Product";
import {
  getProduct,
  getAllProducts,
} from "../../../../store/product/productSlice";
import CheckoutModal from "../../../Checkout/CheckoutModal";
import Toolbar from "./Toolbar";
import _ from "lodash";

// Desktop/Tablet Components
import ToolBar from "./components/ToolBar";
import SidePanel from "./components/SidePanel";
import CanvasArea from "./components/CanvasArea";
import FloatingActionButton from "./components/floatingButtons/FloatingActionButton";
import ColorPickerComponent from "./components/ColorPickerComponent";
import CropTool from "./components/CropTool";
import CanvasInitializer from "./components/CanvasInitializer";
import PrintDimensionsDisplay from "./components/PrintDimensionsDisplay";
import ImageEnhancementTool from "../../../../components/ImageEnhancementTool";
import EnhanceImageButton from "../../../../components/EnhanceImageButton";
import LayersPanel from "./components/Layers/LayersPanel";

// Mobile Components
import MobileSidePanel from "./components/mobile/MobileSidePanel";
import MobileToolBar from "./components/mobile/MobileToolBar";
import MobileFloatingActionButton from "./components/mobile/MobileFloatingActionButton";
import MobileQuickTools from "./components/mobile/MobileQuickTools";

// Utilities
import {
  saveCanvasState,
  getCanvasState,
  removeCanvasState,
  saveImageUploaderPairs,
  getImageUploaderPairs,
  addImageUploaderPair,
  removeImageUploaderPairs,
} from "../../../../utils/storageUtils";
import {
  moveLayerUp,
  moveLayerDown,
  bringToFront,
  sendToBack,
  reorderLayers,
} from "./components/Layers/LayerUtils";
import {
  isMobileDevice,
  isTabletDevice,
  getDeviceType,
} from "../../../../utils/responsiveUtils";

const FONT_FAMILY = [
  "Arial",
  "Helvetica",
  "Times New Roman",
  "Georgia",
  "Open Sans",
  "Roboto",
  "Lato",
  "Montserrat",
  "Raleway",
  "Source Sans Pro",
];

// Constants for conversion
const DEFAULT_DPI = 300; // 300 pixels per inch for print quality
const STANDARD_PPI = 96; // 96 pixels per inch for digital displays

const Prod = () => {
  const { product, products } = useSelector((state) => state.product);
  const { id } = useParams();
  const [testCanvas, setCanvas] = useState();
  const [selectedImage, setSelectedImage] = useState();
  const [viewPreview, setViewPreview] = useState(false);
  const [addedObject, setAddedObject] = useState([]);
  const [selectedFontColor, setSelectedFontColor] = useState("#000000");
  const [selectedFontFamily, setSelectedFontFamily] = useState(FONT_FAMILY[0]);
  const [fontFamily, setFontFamily] = useState(false);
  const [flipState, setFlipState] = useState(false);
  const [canvasStateA, setCanvasStateA] = useState(null);
  const [canvasStateB, setCanvasStateB] = useState(null);
  const [displayShapes, setDisplayShapes] = useState(false);
  const [activeComponent, setActiveComponent] = useState(null);
  const [enlargedScale, setEnlargedScale] = useState(1);
  const [drawWidth, setDrawWidth] = useState(200);
  const [drawHeight, setDrawHeight] = useState(400);
  const [drawWidthInches, setDrawWidthInches] = useState(12.5); // Teespring standard width
  const [drawHeightInches, setDrawHeightInches] = useState(16.5); // Teespring standard height
  const [dpi, setDpi] = useState(DEFAULT_DPI);
  const [selectedObject, setSelectedObject] = useState(null);
  const [showDimensions, setShowDimensions] = useState(true);
  const [undoStack, setUndoStack] = useState([]);
  const [redoStack, setRedoStack] = useState([]);
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [showProductSelector, setShowProductSelector] = useState(false);
  const [isModalVisible, setModalVisible] = useState(false);
  const [checkoutData, setCheckoutData] = useState({});
  const [selectedColors, setSelectedColors] = useState([]);
  const [isCropping, setIsCropping] = useState(false);
  const [isRemovingBackground, setIsRemovingBackground] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [imageIds, setImageIds] = useState([]);
  const [combinedImageIds, setCombinedImageIds] = useState([]);
  const [imageUploaderPairs, setImageUploaderPairs] = useState([]);
  const [combinedImageUploaderPairs, setCombinedImageUploaderPairs] = useState(
    []
  );
  const { fromAffiliate } = location.state || {};

  // Mobile-specific state
  const [isMobile, setIsMobile] = useState(false);
  const [showMobileSidePanel, setShowMobileSidePanel] = useState(false);
  const [showMobileToolbar, setShowMobileToolbar] = useState(false);

  const containerRef = useRef(null);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Prevent browser zoom (pinch, ctrl+wheel, double-tap) on this page
  useEffect(() => {
    // Prevent zoom on wheel + ctrl (desktop)
    const handleWheel = (e) => {
      if (e.ctrlKey) {
        e.preventDefault();
      }
    };
    // Prevent pinch-zoom and double-tap zoom (mobile)
    const handleTouchMove = (e) => {
      if (e.touches && e.touches.length > 1) {
        e.preventDefault();
      }
    };
    const handleGestureStart = (e) => {
      e.preventDefault();
    };
    const node = containerRef.current;
    if (node) {
      node.addEventListener("wheel", handleWheel, { passive: false });
      node.addEventListener("touchmove", handleTouchMove, { passive: false });
      node.addEventListener("gesturestart", handleGestureStart, {
        passive: false,
      });
    }
    // Add viewport meta tag to prevent zoom (for mobile)
    let metaTag = document.querySelector('meta[name="viewport"]');
    let oldMetaContent = null;
    if (!metaTag) {
      metaTag = document.createElement("meta");
      metaTag.name = "viewport";
      document.head.appendChild(metaTag);
    } else {
      oldMetaContent = metaTag.content;
    }
    // Prevent zooming
    metaTag.content =
      "width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no";
    return () => {
      if (node) {
        node.removeEventListener("wheel", handleWheel);
        node.removeEventListener("touchmove", handleTouchMove);
        node.removeEventListener("gesturestart", handleGestureStart);
      }
      // Restore old meta content if it existed
      if (metaTag && oldMetaContent !== null) {
        metaTag.content = oldMetaContent;
      }
    };
  }, []);
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkMobile();

    // Add resize listener
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  useEffect(() => {
    dispatch(getProduct(id));
    dispatch(getAllProducts());

    // Load image-uploader pairs from localStorage if coming from shop/favorites
    const savedPairs = getImageUploaderPairs();
    if (savedPairs.length > 0) {
      console.log("Loaded image-uploader pairs from localStorage:", savedPairs);
      setImageUploaderPairs(savedPairs);
    }
  }, [dispatch, id]);

  useEffect(() => {
    const restoreCanvasState = async () => {
      if (!testCanvas) return;

      // Check if we're returning from Shop/Favorites without selecting an image
      const isReturningFromShopOrFavorites =
        location.state?.fromShopOrFavorites === true &&
        !location.state?.selectedImageUrl;

      // If we're returning from Shop/Favorites without an image, restore from session storage
      if (isReturningFromShopOrFavorites) {
        console.log(
          "Returning from Shop/Favorites without selecting an image, restoring canvas state"
        );

        // Load the saved canvas states from session storage
        const savedCanvasStateA = getCanvasState("canvasStateA");
        const savedCanvasStateB = getCanvasState("canvasStateB");

        if (savedCanvasStateA) {
          console.log(
            "Restoring front canvas state with",
            savedCanvasStateA.objects?.length || 0,
            "objects"
          );
          setCanvasStateA(savedCanvasStateA);

          // If we're on the front side, load the state to the canvas
          if (!flipState) {
            testCanvas.loadFromJSON(savedCanvasStateA, () => {
              const existingObjects = testCanvas.getObjects();
              setAddedObject(existingObjects);
              testCanvas.renderAll();
              console.log(
                "Front canvas state restored with",
                existingObjects.length,
                "objects"
              );
            });
          }
        }

        if (savedCanvasStateB) {
          console.log(
            "Restoring back canvas state with",
            savedCanvasStateB.objects?.length || 0,
            "objects"
          );
          setCanvasStateB(savedCanvasStateB);

          // If we're on the back side, load the state to the canvas
          if (flipState) {
            testCanvas.loadFromJSON(savedCanvasStateB, () => {
              const existingObjects = testCanvas.getObjects();
              setAddedObject(existingObjects);
              testCanvas.renderAll();
              console.log(
                "Back canvas state restored with",
                existingObjects.length,
                "objects"
              );
            });
          }
        }

        // Clear session storage after loading
        removeCanvasState("canvasStateA");
        removeCanvasState("canvasStateB");
        removeCanvasState("canvasState");

        return;
      }

      // Only run this effect when there's a new image or saved design
      // AND when the image hasn't been processed yet
      if (
        (!location.state?.selectedImageUrl && !location.state?.savedDesign) ||
        location.state?.imageProcessed
      ) {
        return;
      }

      // Handle loading from saved designs
      if (location.state?.savedDesign) {
        console.log("Loading saved design");
        console.log("Location state:", location.state);
        console.log(
          "tshirtFacing from location state:",
          location.state?.tshirtFacing
        );
        console.log(
          "Active design side:",
          location.state?.activeDesign ||
            (location.state?.tshirtFacing ? "back" : "front")
        );

        // Get the tshirt facing from location state
        const shouldUseTshirtBack =
          location.state?.tshirtFacing !== undefined
            ? location.state.tshirtFacing
            : flipState;

        // Update flip state and shirt image if needed
        if (shouldUseTshirtBack !== flipState) {
          console.log(
            "Updating flip state from",
            flipState,
            "to",
            shouldUseTshirtBack
          );
          setFlipState(shouldUseTshirtBack);
          document.getElementById("tshirtFacing").src = shouldUseTshirtBack
            ? product?.imageBack
            : product?.imageFront;
        }

        // Always set both canvas states
        console.log("Setting canvas states from saved design");

        // Check if savedDesign exists and is valid
        if (
          location.state.savedDesign &&
          typeof location.state.savedDesign === "object"
        ) {
          console.log(
            "Setting canvasStateA with",
            location.state.savedDesign.objects?.length || 0,
            "objects"
          );
          setCanvasStateA(location.state.savedDesign);
        } else {
          console.log("No valid front design found");
          // Set an empty state for front if none exists
          setCanvasStateA({
            version: "5.3.0",
            objects: [],
            background: "transparent",
          });
        }

        // Check if savedDesignBack exists and is valid
        if (
          location.state.savedDesignBack &&
          typeof location.state.savedDesignBack === "object"
        ) {
          console.log(
            "Setting canvasStateB with",
            location.state.savedDesignBack.objects?.length || 0,
            "objects"
          );
          setCanvasStateB(location.state.savedDesignBack);
        } else {
          console.log("No valid back design found");
          // Set an empty state for back if none exists
          setCanvasStateB({
            version: "5.3.0",
            objects: [],
            background: "transparent",
          });
        }

        // Load the appropriate design based on the tshirt facing
        if (shouldUseTshirtBack) {
          console.log("Loading back design");
          if (location.state.savedDesignBack) {
            testCanvas.loadFromJSON(location.state.savedDesignBack, () => {
              const existingObjects = testCanvas.getObjects();
              setAddedObject(existingObjects);
              testCanvas.renderAll();
              console.log(
                "Back design loaded with",
                existingObjects.length,
                "objects"
              );
            });
          } else {
            console.log("No back design available");
            testCanvas.clear();
            setAddedObject([]);
            testCanvas.renderAll();
          }
        } else {
          console.log("Loading front design");
          testCanvas.loadFromJSON(location.state.savedDesign, () => {
            const existingObjects = testCanvas.getObjects();
            setAddedObject(existingObjects);
            testCanvas.renderAll();
            console.log(
              "Front design loaded with",
              existingObjects.length,
              "objects"
            );
          });
        }
      }
      // Handle image from shop/favorites
      else if (location.state?.selectedImageUrl) {
        console.log("Loading image from shop/favorites - initial load");
        console.log("Location state:", location.state);
        console.log("Selected image URL:", location.state.selectedImageUrl);
        console.log("Selected image ID:", location.state.selectedImageId);
        console.log("Uploader ID:", location.state.uploaderId);
        console.log(
          "tshirtFacing from location state:",
          location.state?.tshirtFacing
        );

        // Set the image-uploader pair if both exist
        if (location.state.selectedImageId && location.state.uploaderId) {
          // Create the pair object
          const newPair = {
            imageId: location.state.selectedImageId,
            uploader: location.state.uploaderId,
          };

          // Add to state
          setImageUploaderPairs((prev) => {
            // Check if this pair already exists
            const pairExists = prev.some(
              (pair) =>
                pair.imageId === location.state.selectedImageId &&
                pair.uploader === location.state.uploaderId
            );

            // Add the pair if it doesn't exist
            if (!pairExists) {
              // Also add to localStorage for persistence
              addImageUploaderPair(newPair);

              return [...prev, newPair];
            }
            return prev;
          });
        }

        // Get the tshirt facing from location state
        const shouldUseTshirtBack =
          location.state?.tshirtFacing !== undefined
            ? location.state.tshirtFacing
            : flipState;

        console.log("Using tshirt facing (back):", shouldUseTshirtBack);

        // Update flip state and shirt image if needed
        if (shouldUseTshirtBack !== flipState) {
          console.log(
            "Updating flip state from",
            flipState,
            "to",
            shouldUseTshirtBack
          );
          setFlipState(shouldUseTshirtBack);
          document.getElementById("tshirtFacing").src = shouldUseTshirtBack
            ? product?.imageBack
            : product?.imageFront;
        }

        // Load the saved canvas states from session storage using our utility
        const savedCanvasStateA = getCanvasState("canvasStateA");
        const savedCanvasStateB = getCanvasState("canvasStateB");

        console.log(
          "Saved states from session storage:",
          savedCanvasStateA
            ? `A: ${savedCanvasStateA.objects?.length || 0} objects`
            : "A: none",
          savedCanvasStateB
            ? `B: ${savedCanvasStateB.objects?.length || 0} objects`
            : "B: none"
        );

        // Update the canvas states with the saved states
        if (savedCanvasStateA) {
          setCanvasStateA(savedCanvasStateA);
        }
        if (savedCanvasStateB) {
          setCanvasStateB(savedCanvasStateB);
        }

        // Clear session storage after loading
        removeCanvasState("canvasStateA");
        removeCanvasState("canvasStateB");
        removeCanvasState("canvasState");

        // Determine which state to use based on the tshirt facing
        const stateToUse = shouldUseTshirtBack
          ? savedCanvasStateB || canvasStateB
          : savedCanvasStateA || canvasStateA;

        console.log(
          "Using state for side:",
          shouldUseTshirtBack ? "Back" : "Front",
          "with",
          stateToUse?.objects?.length || 0,
          "objects"
        );

        // If we have a state to use, load it first
        if (stateToUse && stateToUse.objects && stateToUse.objects.length > 0) {
          console.log("Loading existing state before adding new image");

          // Load the existing state
          testCanvas.loadFromJSON(stateToUse, () => {
            const existingObjects = testCanvas.getObjects();
            setAddedObject(existingObjects);
            console.log(
              "Loaded canvas with",
              existingObjects.length,
              "objects"
            );

            // Check if the image already exists in the canvas
            const imageExists = existingObjects.some(
              (obj) =>
                obj.type === "image" &&
                obj.imageId === location.state.selectedImageId
            );

            // Add the new image if it doesn't exist
            if (!imageExists) {
              console.log("Adding new image to canvas with existing objects");
              addImageToCanvas(
                location.state.selectedImageUrl,
                location.state.selectedImageId,
                shouldUseTshirtBack
              );
            } else {
              console.log("Image already exists in canvas, not adding again");
            }

            // Mark the image as processed
            const updatedState = {
              ...location.state,
              selectedImageUrl: null,
              selectedImageId: null,
              imageProcessed: true,
            };
            window.history.replaceState(updatedState, document.title);
          });
        } else {
          // If no state exists, just add the image to an empty canvas
          console.log("No existing state found, adding image to empty canvas");
          testCanvas.clear();
          addImageToCanvas(
            location.state.selectedImageUrl,
            location.state.selectedImageId,
            shouldUseTshirtBack
          );

          // Mark the image as processed
          window.history.replaceState(
            {
              ...location.state,
              selectedImageUrl: null,
              selectedImageId: null,
              imageProcessed: true,
            },
            document.title
          );
        }
      }
    };

    // Helper function to get proxy URL for OBS images
    const getProxyUrlIfNeeded = (imageUrl) => {
      // Check if this is an OBS image that might need proxy
      if (imageUrl.includes('obsv3.et-global-1.ethiotelecom.et')) {
        // Extract object key from OBS URL
        // OBS URL format: https://product-images.obsv3.et-global-1.ethiotelecom.et/bucket-name/folder/filename
        const urlParts = imageUrl.split('/');
        // Find the bucket name and get everything after it
        const bucketIndex = urlParts.findIndex(part => part.includes('obsv3.et-global-1.ethiotelecom.et'));
        if (bucketIndex !== -1 && bucketIndex + 2 < urlParts.length) {
          // Get the object key (everything after bucket name)
          const objectKey = urlParts.slice(bucketIndex + 2).join('/');
          const proxyUrl = `/api/v1/images/obs-proxy/${encodeURIComponent(objectKey)}`;
          console.log("🔄 Using proxy URL for OBS image:", proxyUrl);
          console.log("Original URL:", imageUrl);
          console.log("Extracted object key:", objectKey);
          return proxyUrl;
        }
      }
      return imageUrl;
    };

    const addImageToCanvas = (
      imageUrl,
      imageId,
      shouldUseTshirtBack = undefined
    ) => {
      console.log("Adding image to canvas:", {
        imageUrl,
        imageId,
        shouldUseTshirtBack,
      });

      // Use proxy URL for OBS images to avoid CORS issues
      const finalImageUrl = getProxyUrlIfNeeded(imageUrl);
      console.log("Final image URL:", finalImageUrl);

      fabric.Image.fromURL(
        finalImageUrl,
        (img) => {
          console.log("Image loaded successfully");
          const canvasWidth = testCanvas.width;
          const canvasHeight = testCanvas.height;
          const imgAspectRatio = img.width / img.height;
          const canvasAspectRatio = canvasWidth / canvasHeight;

          let scaleFactor;
          if (imgAspectRatio > canvasAspectRatio) {
            scaleFactor = (canvasWidth / img.width) * 0.8;
          } else {
            scaleFactor = (canvasHeight / img.height) * 0.8;
          }

          img.scale(scaleFactor);
          img.set({
            left: canvasWidth / 2,
            top: canvasHeight / 2,
            originX: "center",
            originY: "center",
            crossOrigin: "anonymous",
            imageId: imageId,
          });

          testCanvas.add(img);
          testCanvas.setActiveObject(img);
          console.log("Image added to canvas");

          // Save current state to appropriate side
          const currentState = testCanvas.toJSON([
            "id",
            "selectable",
            "left",
            "top",
            "scaleX",
            "scaleY",
            "angle",
            "flipX",
            "flipY",
            "imageId",
          ]);

          // Use the provided tshirt facing if available, otherwise use the current flipState
          const currentTshirtFacing =
            shouldUseTshirtBack !== undefined ? shouldUseTshirtBack : flipState;

          console.log("Current tshirt facing (back):", currentTshirtFacing);
          console.log("Current canvas states before update:", {
            A: canvasStateA?.objects?.length || 0,
            B: canvasStateB?.objects?.length || 0,
          });

          if (currentTshirtFacing) {
            console.log("Saving to canvas state B (back)");
            setCanvasStateB(currentState);
          } else {
            console.log("Saving to canvas state A (front)");
            setCanvasStateA(currentState);
          }

          // Update added objects
          const allObjects = testCanvas.getObjects();
          setAddedObject(allObjects);
          console.log("Added objects updated, count:", allObjects.length);

          testCanvas.renderAll();
        },
        {
          crossOrigin: "anonymous",
          // Add error handler for failed image loads
          onError: (error) => {
            console.error("❌ Failed to load image:", finalImageUrl, error);
            console.error("Original URL:", imageUrl);

            // Check if this is an OBS image
            if (imageUrl.includes('obsv3.et-global-1.ethiotelecom.et')) {
              console.log("🔄 OBS image detected - proxy was attempted");
              console.log("💡 This might be a network issue or the proxy endpoint is not working");
            }

            // Show user-friendly error
            alert(`Failed to load image. Please check your network connection or try again later. Original URL: ${imageUrl}`);
          }
        }
      );
    };

    restoreCanvasState();
  }, [
    location.state?.selectedImageUrl,
    location.state?.savedDesign,
    location.state?.savedDesignBack,
    testCanvas,
    // flipState,
  ]);

  const toggleComponent = (componentName) => {
    setActiveComponent(
      activeComponent === componentName ? null : componentName
    );
  };

  const handleUndo = () => {
    if (undoStack.length > 1) {
      const newUndoStack = [...undoStack];
      const lastState = newUndoStack.pop();
      setRedoStack((prev) => [...prev, lastState]);
      setUndoStack(newUndoStack);
      testCanvas.loadFromJSON(newUndoStack[newUndoStack.length - 1], () => {
        testCanvas.renderAll();
      });
    }
  };

  const handleRedo = () => {
    if (redoStack.length > 0) {
      const newRedoStack = [...redoStack];
      const nextState = newRedoStack.pop();
      setUndoStack((prev) => [...prev, nextState]);
      setRedoStack(newRedoStack);
      testCanvas.loadFromJSON(nextState, () => {
        testCanvas.renderAll();
      });
    }
  };

  // const handleFileUp = (e) => {
  //   const reader = new FileReader();
  //   reader.onload = function (e) {
  //     let image = new Image();
  //     image.src = e.target.result;
  //     image.onload = function () {
  //       const img = new fabric.Image(image, {
  //         crossOrigin: "anonymous",
  //         objectCaching: false,
  //         scaleX: 1,
  //         scaleY: 1,
  //       });

  //       const scale = Math.min(
  //         testCanvas.width / (img.width * enlargedScale),
  //         testCanvas.height / (img.height * enlargedScale)
  //       );
  //       img.scale(scale);
  //       console.log(img);

  //       testCanvas.add(img);
  //       testCanvas.setActiveObject(img);

  //       // Update addedObject state by getting ALL canvas objects
  //       const allObjects = testCanvas.getObjects();
  //       setAddedObject(allObjects);

  //       testCanvas.renderAll();
  //     };
  //   };
  //   reader.readAsDataURL(e.target.files[0]);
  // };

  const handleFileUp = (e) => {
    const reader = new FileReader();
    reader.onload = function (e) {
      let image = new Image();
      image.src = e.target.result;
      image.onload = function () {
        // Adjust canvas size or calculate scale factor
        const scale = Math.min(
          testCanvas.width / image.width,
          testCanvas.height / image.height
        );

        // Generate a unique ID for the uploaded image
        const uniqueImageId = `uploaded-${Date.now()}-${Math.floor(
          Math.random() * 1000
        )}`;

        const img = new fabric.Image(image, {
          crossOrigin: "anonymous",
          objectCaching: false,
          scaleX: scale,
          scaleY: scale,
          imageId: uniqueImageId, // Assign the unique ID
        });

        console.log("Uploaded image assigned imageId:", uniqueImageId);

        testCanvas.add(img);
        testCanvas.setActiveObject(img);

        // Update addedObject state
        const allObjects = testCanvas.getObjects();
        setAddedObject(allObjects);

        // Ensure high-quality rendering
        testCanvas.getContext().imageSmoothingEnabled = true;
        testCanvas.getContext().imageSmoothingQuality = "high";

        testCanvas.renderAll();
      };
    };
    reader.readAsDataURL(e.target.files[0]);
  };

  const addObject = (object) => {
    setAddedObject((prevObjects) => [...prevObjects, object]);
  };

  const handleObjectSelection = (object) => {
    try {
      // Validate input
      if (!object || typeof object !== "object") {
        console.warn("Invalid object passed to handleObjectSelection");
        return;
      }

      // Check if the canvas is available
      if (!testCanvas) {
        console.warn("Canvas not available");
        return;
      }

      // Check if the object is still valid and exists in the canvas
      const canvasObjects = testCanvas.getObjects();
      const objectExists = canvasObjects.includes(object);

      if (objectExists) {
        // Make sure the object has all required properties and methods
        if (object.setCoords && typeof object.setCoords === "function") {
          // Ensure object coordinates are up to date
          object.setCoords();
        }

        // Set active property on the selected object and remove it from others
        testCanvas.getObjects().forEach((obj) => {
          obj.set("active", false);
        });
        object.set("active", true);

        testCanvas.setActiveObject(object);
        testCanvas.renderAll();

        // Update the addedObject state to reflect the active object
        setAddedObject([...testCanvas.getObjects()]);
      } else {
        console.warn("Object not found in canvas, updating layers panel");
        // Update the addedObject state to remove invalid objects
        setAddedObject((prevObjects) => {
          // Filter out any objects that don't exist in the canvas
          const validObjects = prevObjects.filter((obj) =>
            canvasObjects.includes(obj)
          );
          console.log(
            `Filtered layers panel: ${prevObjects.length} -> ${validObjects.length} objects`
          );
          return validObjects;
        });
      }
    } catch (error) {
      console.error("Error selecting object:", error);
      // Update the addedObject state to ensure it's in sync with the canvas
      try {
        const canvasObjects = testCanvas.getObjects();
        setAddedObject(canvasObjects);
      } catch (e) {
        console.error("Error updating addedObject state:", e);
      }
    }
  };

  const saveCurrentCanvasState = () => {
    if (!testCanvas) return;

    const objects = testCanvas.getObjects();
    // Include imageId in the properties to save
    const currentState = testCanvas.toJSON([
      "id",
      "selectable",
      "left",
      "top",
      "scaleX",
      "scaleY",
      "angle",
      "flipX",
      "flipY",
      "imageId",
    ]);

    console.log(
      `Attempting to save state ${flipState ? "B" : "A"} with objects:`,
      objects.length
    );

    // Log objects with imageId for debugging and add to imageIds state
    objects.forEach((obj, index) => {
      if (obj.type === "image") {
        console.log(
          `Object ${index} (${obj.type}) imageId:`,
          obj.imageId || "none"
        );

        // Add imageId to state if it exists and is not already in the array
        if (obj.imageId) {
          setImageIds((prevIds) => {
            // Only add if not already in the array
            if (!prevIds.includes(obj.imageId)) {
              return [...prevIds, obj.imageId];
            }
            return prevIds;
          });
        }
      }
    });

    // Only save to the current side's state
    if (flipState) {
      setCanvasStateB(currentState);
    } else {
      setCanvasStateA(currentState);
    }
  };

  const handleFlipClick = () => {
    if (!product?.imageBack) {
      console.log("No back image available, can't flip");
      return;
    }

    console.log(
      "Flipping from",
      flipState ? "back" : "front",
      "to",
      !flipState ? "back" : "front"
    );
    console.log("Current canvas states BEFORE flipping:", {
      A: canvasStateA?.objects?.length || 0,
      B: canvasStateB?.objects?.length || 0,
    });

    // Save current state before flipping with all properties
    const currentState = testCanvas.toJSON([
      "id",
      "selectable",
      "left",
      "top",
      "scaleX",
      "scaleY",
      "angle",
      "flipX",
      "flipY",
      "imageId",
    ]);

    const currentObjects = testCanvas.getObjects();
    console.log("Current canvas has", currentObjects.length, "objects");

    // Important: Store the current state to the appropriate side BEFORE updating flipState
    if (flipState) {
      console.log("Currently on BACK side, saving state to canvasStateB");
      console.log("Current state objects:", currentState.objects?.length || 0);
      setCanvasStateB(currentState);
    } else {
      console.log("Currently on FRONT side, saving state to canvasStateA");
      console.log("Current state objects:", currentState.objects?.length || 0);
      setCanvasStateA(currentState);
    }

    // Clear canvas
    testCanvas.clear();

    // Update flip state
    const newFlipState = !flipState;
    setFlipState(newFlipState);

    // Update the shirt image
    document.getElementById("tshirtFacing").src = newFlipState
      ? product?.imageBack
      : product?.imageFront;

    // Important: We need to use the state variables directly, not the ones we just updated
    // because React state updates are asynchronous
    // Create a local copy of the state to load to avoid issues with React's asynchronous state updates
    const stateToLoad = newFlipState
      ? { ...canvasStateB }
      : { ...canvasStateA };

    console.log(
      `Loading ${newFlipState ? "BACK" : "FRONT"} state with objects:`,
      stateToLoad?.objects?.length || 0
    );

    // Debug the state we're loading
    if (stateToLoad) {
      console.log("State to load:", {
        version: stateToLoad.version,
        objectCount: stateToLoad.objects?.length || 0,
        background: stateToLoad.background,
      });
    } else {
      console.log("State to load is null or undefined");
    }

    // Check if we're coming from saved designs
    const isComingFromSavedDesigns =
      location.state?.savedDesign || location.state?.savedDesignBack;

    // If we're flipping to the back side and there's no state or no objects, check if we have a saved back design
    if (
      newFlipState &&
      (!stateToLoad || !stateToLoad.objects || stateToLoad.objects.length === 0)
    ) {
      // If we're coming from saved designs and have a saved back design, use it
      if (isComingFromSavedDesigns && location.state?.savedDesignBack) {
        console.log("Using saved back design from location state");
        const savedBackDesign = location.state.savedDesignBack;
        console.log(
          "Saved back design has",
          savedBackDesign.objects?.length || 0,
          "objects"
        );

        // Set the canvas state B with the saved back design
        setCanvasStateB(savedBackDesign);

        // Load the saved back design
        testCanvas.loadFromJSON(savedBackDesign, () => {
          const loadedObjects = testCanvas.getObjects();
          setAddedObject(loadedObjects);
          testCanvas.renderAll();
          console.log(
            "Loaded saved back design with",
            loadedObjects.length,
            "objects"
          );
        });
      } else {
        console.log(
          "No back state found or back state has no objects, creating an empty one"
        );
        // Create an empty state for the back side
        const emptyBackState = {
          version: "5.3.0",
          objects: [],
          background: "transparent",
        };
        setCanvasStateB(emptyBackState);

        // Use the empty state
        console.log("Using empty back state");
        testCanvas.renderAll();
        setAddedObject([]);
      }
    } else if (stateToLoad && stateToLoad.objects) {
      console.log("Loading state with objects:", stateToLoad.objects.length);

      // Log each object in the state
      stateToLoad.objects.forEach((obj, index) => {
        console.log(`Object ${index}:`, {
          type: obj.type,
          imageId: obj.imageId,
          left: obj.left,
          top: obj.top,
        });
      });

      testCanvas.loadFromJSON(stateToLoad, () => {
        // Update the addedObject state with the loaded objects
        const loadedObjects = testCanvas.getObjects();
        setAddedObject(loadedObjects);

        testCanvas.renderAll();
        testCanvas.calcOffset();
        console.log(
          "State loaded successfully, canvas now has",
          testCanvas.getObjects().length,
          "objects"
        );
      });
    } else {
      console.log(
        "No state to load for",
        newFlipState ? "back" : "front",
        "side"
      );
      // Make sure addedObject is cleared when there's no state to load
      setAddedObject([]);
      testCanvas.renderAll();
    }

    // Log the state after flipping
    console.log("Canvas states AFTER flipping:", {
      A: canvasStateA?.objects?.length || 0,
      B: canvasStateB?.objects?.length || 0,
    });
  };

  useEffect(() => {
    if (!testCanvas) return;

    let modificationTimeout = null;

    const handleCanvasModification = () => {
      // Clear any pending timeout
      if (modificationTimeout) {
        clearTimeout(modificationTimeout);
      }

      // Set a new timeout to handle the modification
      modificationTimeout = setTimeout(() => {
        const objects = testCanvas.getObjects();
        console.log("Canvas modified, current objects:", objects.length);

        // Log image objects and their imageIds for debugging
        objects.forEach((obj, index) => {
          if (obj.type === "image") {
            console.log(
              `Modified object ${index} (${obj.type}) imageId:`,
              obj.imageId || "none"
            );

            // Add imageId to state if it exists and is not already in the array
            if (obj.imageId) {
              setImageIds((prevIds) => {
                // Only add if not already in the array
                if (!prevIds.includes(obj.imageId)) {
                  return [...prevIds, obj.imageId];
                }
                return prevIds;
              });
            }
          }
        });

        // Only save if we have objects and we're not in the middle of a flip
        if (objects.length > 0) {
          saveCurrentCanvasState();
        }
      }, 100); // Small delay to ensure all modifications are complete
    };

    // Add a selection event handler to update the active property
    const handleSelectionCreated = (e) => {
      // Clear active property from all objects
      testCanvas.getObjects().forEach((obj) => {
        obj.set("active", false);
      });

      // Set active property on the selected object(s)
      if (e.selected) {
        e.selected.forEach((obj) => {
          obj.set("active", true);
        });
      }

      // Update the addedObject state to reflect the active object
      setAddedObject([...testCanvas.getObjects()]);
    };

    // Add a selection cleared event handler
    const handleSelectionCleared = () => {
      // Clear active property from all objects
      testCanvas.getObjects().forEach((obj) => {
        obj.set("active", false);
      });

      // Update the addedObject state
      setAddedObject([...testCanvas.getObjects()]);
    };

    // Add event listeners for selection events
    testCanvas.on("selection:created", handleSelectionCreated);
    testCanvas.on("selection:updated", handleSelectionCreated);
    testCanvas.on("selection:cleared", handleSelectionCleared);

    const events = [
      "object:modified",
      "object:added",
      "object:removed",
      "path:created",
    ];

    events.forEach((event) => {
      testCanvas.on(event, handleCanvasModification);
    });

    return () => {
      events.forEach((event) => {
        testCanvas.off(event, handleCanvasModification);
      });

      // Remove selection event listeners
      testCanvas.off("selection:created", handleSelectionCreated);
      testCanvas.off("selection:updated", handleSelectionCreated);
      testCanvas.off("selection:cleared", handleSelectionCleared);

      if (modificationTimeout) {
        clearTimeout(modificationTimeout);
      }
    };
  }, [testCanvas, flipState]);

  // Initialize canvas states
  useEffect(() => {
    console.log("initialize executed");
    if (testCanvas) {
      // Check if we have saved states in session storage using our utility
      const savedCanvasStateA = getCanvasState("canvasStateA");
      const savedCanvasStateB = getCanvasState("canvasStateB");

      // Create an empty state as fallback
      const emptyState = testCanvas.toJSON(["id", "selectable"]);

      // Flag to track if we're coming from shop/favorites with a selected image
      const isComingFromShopOrFavorites = location.state?.selectedImageUrl;
      // Flag to track if we're coming from saved designs
      const isComingFromSavedDesigns =
        location.state?.savedDesign || location.state?.savedDesignBack;

      console.log(
        "Coming from saved designs?",
        Boolean(isComingFromSavedDesigns)
      );

      // Only initialize with empty states if we don't have existing states
      // and we're not coming from shop/favorites or saved designs
      if (!isComingFromShopOrFavorites && !isComingFromSavedDesigns) {
        console.log(
          "Normal initialization - no saved designs or shop/favorites"
        );

        // Initialize canvasStateA
        if (savedCanvasStateA) {
          console.log("Loading state A from session storage");
          setCanvasStateA(savedCanvasStateA);
        } else if (canvasStateA === null) {
          console.log("Initializing state A with empty state");
          setCanvasStateA(emptyState);
        }

        // Initialize canvasStateB
        if (savedCanvasStateB) {
          console.log("Loading state B from session storage");
          console.log(
            "State B has",
            savedCanvasStateB.objects?.length || 0,
            "objects"
          );
          setCanvasStateB(savedCanvasStateB);
        } else if (canvasStateB === null) {
          console.log("Initializing state B with empty state");
          // Make sure we create a proper empty state with objects array
          const emptyBackState = {
            ...emptyState,
            objects: [],
            background: "transparent",
          };
          console.log("Empty back state created:", emptyBackState);
          setCanvasStateB(emptyBackState);
        }

        // Clear session storage after loading
        removeCanvasState("canvasStateA");
        removeCanvasState("canvasStateB");
        removeCanvasState("canvasState");
      } else if (isComingFromSavedDesigns) {
        console.log("Coming from saved designs, preserving canvas states");
        // Don't initialize with empty states when coming from saved designs
        // This will be handled in the loadSavedDesign logic
      } else {
        console.log("Coming from shop/favorites, preserving canvas states");
        // Don't clear session storage or initialize with empty states
        // when coming from shop/favorites - this will be handled in the image loading logic
      }
    }
  }, [testCanvas, location.state]);

  // Utility function to get all image IDs from the canvas
  const getImageIdsFromCanvas = () => {
    if (!testCanvas) return [];
    const objects = testCanvas.getObjects();
    const ids = [];

    objects.forEach((obj) => {
      if (obj.type === "image" && obj.imageId && !ids.includes(obj.imageId)) {
        ids.push(obj.imageId);
      }
    });

    return ids;
  };

  // Function to synchronize the imageIds state with the canvas
  const syncImageIdsWithCanvas = () => {
    if (!testCanvas) return;
    const currentIds = getImageIdsFromCanvas();
    setImageIds(currentIds);
  };

  // Function to get image IDs from a canvas state object
  const getImageIdsFromCanvasState = (canvasState) => {
    if (!canvasState || !canvasState.objects) return [];

    const ids = [];
    canvasState.objects.forEach((obj) => {
      if (obj.type === "image" && obj.imageId && !ids.includes(obj.imageId)) {
        ids.push(obj.imageId);
      }
    });

    return ids;
  };

  // Function to combine image IDs from both canvases (front and back)
  const updateCombinedImageIds = () => {
    // Get IDs from front canvas (canvasStateA)
    const frontIds = getImageIdsFromCanvasState(canvasStateA);

    // Get IDs from back canvas (canvasStateB)
    const backIds = getImageIdsFromCanvasState(canvasStateB);

    // Combine and deduplicate IDs
    const allIds = [...frontIds, ...backIds];
    const uniqueIds = [...new Set(allIds)];

    // Update the combined IDs state
    setCombinedImageIds(uniqueIds);

    console.log("Combined image IDs updated:", uniqueIds);
    return uniqueIds;
  };

  // Function to update combined image-uploader pairs
  const updateCombinedImageUploaderPairs = () => {
    // Create a map to deduplicate pairs by imageId
    const pairsMap = new Map();

    // Add all pairs to the map, using imageId as key
    imageUploaderPairs.forEach((pair) => {
      pairsMap.set(pair.imageId, pair);
    });

    // Convert map values back to array
    const uniquePairs = Array.from(pairsMap.values());

    // Update the combined pairs state
    setCombinedImageUploaderPairs(uniquePairs);

    console.log("Combined image-uploader pairs updated:", uniquePairs);
    return uniquePairs;
  };

  // Effect to synchronize imageIds with canvas when canvas changes
  useEffect(() => {
    if (testCanvas) {
      syncImageIdsWithCanvas();
    }
  }, [testCanvas, addedObject]);

  // Debug effect for image IDs
  useEffect(() => {
    console.log("Image IDs updated:", imageIds);
  }, [imageIds]);

  // Effect to update combined image IDs when canvas states change
  useEffect(() => {
    if (canvasStateA || canvasStateB) {
      updateCombinedImageIds();
    }
  }, [canvasStateA, canvasStateB]);

  // Debug effect for combined image IDs
  useEffect(() => {
    console.log("Combined Image IDs updated:", combinedImageIds);
  }, [combinedImageIds]);

  // Effect to update combined image-uploader pairs when pairs change
  useEffect(() => {
    if (imageUploaderPairs.length > 0) {
      updateCombinedImageUploaderPairs();
    }
  }, [imageUploaderPairs]);

  // Debug effect for combined image-uploader pairs
  useEffect(() => {
    console.log(
      "Combined image-uploader pairs updated:",
      combinedImageUploaderPairs
    );

    // After processing the pairs, remove them from localStorage
    // This ensures we don't keep unnecessary data in localStorage
    if (combinedImageUploaderPairs.length > 0) {
      removeImageUploaderPairs();
    }
  }, [combinedImageUploaderPairs]);

  // Debug effect
  useEffect(() => {
    console.log("console executed");
    if (canvasStateA || canvasStateB) {
      console.log("States updated:");
      console.log("A:", canvasStateA?.objects?.length || 0, "objects");
      console.log("B:", canvasStateB?.objects?.length || 0, "objects");

      // Log detailed information about canvasStateB
      if (canvasStateB) {
        console.log("canvasStateB details:", {
          version: canvasStateB.version,
          objectCount: canvasStateB.objects?.length || 0,
          hasObjects: Boolean(canvasStateB.objects?.length),
          background: canvasStateB.background,
        });

        // Log each object in canvasStateB
        if (canvasStateB.objects && canvasStateB.objects.length > 0) {
          console.log("canvasStateB objects:");
          canvasStateB.objects.forEach((obj, index) => {
            console.log(`Object ${index}:`, {
              type: obj.type,
              imageId: obj.imageId,
              left: obj.left,
              top: obj.top,
            });
          });
        }
      }
    }
  }, [canvasStateA, canvasStateB]);

  // Add a useEffect to ensure canvasStateB is properly initialized
  useEffect(() => {
    if (!testCanvas) return;

    // Check if we're coming from saved designs
    const isComingFromSavedDesigns =
      location.state?.savedDesign || location.state?.savedDesignBack;

    // If we don't have a back state yet, initialize it
    if (!canvasStateB) {
      // If we're coming from saved designs and have a saved back design, use it
      if (isComingFromSavedDesigns && location.state?.savedDesignBack) {
        console.log("Initializing canvasStateB with saved back design");
        setCanvasStateB(location.state.savedDesignBack);
      } else {
        console.log("Initializing canvasStateB with empty state");
        setCanvasStateB({
          version: "5.3.0",
          objects: [],
          background: "transparent",
        });
      }
    }

    // Log the current states for debugging
    console.log("Current canvas states:", {
      A: canvasStateA?.objects?.length || 0,
      B: canvasStateB?.objects?.length || 0,
    });
  }, [testCanvas, canvasStateB, location.state]);

  // Add a useEffect to handle changes in flipState
  useEffect(() => {
    if (!testCanvas || !product) return;

    console.log("Flip state changed to", flipState ? "back" : "front");

    // Update the shirt image
    document.getElementById("tshirtFacing").src = flipState
      ? product?.imageBack || product?.imageFront
      : product?.imageFront;

    // Log the current states
    console.log("Canvas states when flip state changed:", {
      A: canvasStateA?.objects?.length || 0,
      B: canvasStateB?.objects?.length || 0,
    });
  }, [flipState, testCanvas, product]);

  const handleDeleteObject = (object) => {
    try {
      // Validate input
      if (!object || typeof object !== "object") {
        console.warn("Invalid object passed to handleDeleteObject");
        return;
      }

      // Check if the canvas is available
      if (!testCanvas) {
        console.warn("Canvas not available");
        return;
      }

      // Check if the object is still valid and exists in the canvas
      const canvasObjects = testCanvas.getObjects();
      const objectExists = canvasObjects.includes(object);

      if (objectExists) {
        // If it's an image with an imageId, remove it from the imageIds state
        if (object.type === "image" && object.imageId) {
          setImageIds((prevIds) =>
            prevIds.filter((id) => id !== object.imageId)
          );
        }

        testCanvas.remove(object);
        setAddedObject((prev) => prev.filter((o) => o !== object));
        testCanvas.renderAll();

        // Save canvas state after deletion
        saveCurrentCanvasState();
      } else {
        console.warn("Object not found in canvas, updating layers panel");
        // Update the addedObject state to remove invalid objects
        setAddedObject((prevObjects) => {
          // Filter out any objects that don't exist in the canvas
          const validObjects = prevObjects.filter((obj) =>
            canvasObjects.includes(obj)
          );
          console.log(
            `Filtered layers panel: ${prevObjects.length} -> ${validObjects.length} objects`
          );
          return validObjects;
        });
      }
    } catch (error) {
      console.error("Error deleting object:", error);
      // Update the addedObject state to ensure it's in sync with the canvas
      try {
        const canvasObjects = testCanvas.getObjects();
        setAddedObject(canvasObjects);
      } catch (e) {
        console.error("Error updating addedObject state:", e);
      }
    }
  };

  const handleRemoveEverything = () => {
    if (!testCanvas) return;

    if (window.confirm("Are you sure you want to clear the canvas?")) {
      testCanvas.clear();
      setAddedObject([]);
      setSelectedImage(null);
      testCanvas.renderAll();

      // Save canvas state after clearing
      saveCurrentCanvasState();
    }
  };

  // Function to move an object up in the layer stack (bring forward)
  const handleMoveLayerUp = (object) => {
    moveLayerUp(testCanvas, object, setAddedObject);
  };

  // Function to move an object down in the layer stack (send backward)
  const handleMoveLayerDown = (object) => {
    moveLayerDown(testCanvas, object, setAddedObject);
  };

  // Function to bring an object to the top of the layer stack
  const handleBringToFront = (object) => {
    bringToFront(testCanvas, object, setAddedObject);
  };

  // Function to send an object to the bottom of the layer stack
  const handleSendToBack = (object) => {
    sendToBack(testCanvas, object, setAddedObject);
  };

  // Function to reorder objects by drag and drop
  const handleReorderLayers = (sourceIndex, targetIndex) => {
    reorderLayers(testCanvas, sourceIndex, targetIndex, setAddedObject);
  };

  const handleAddFromShop = () => {
    console.log("Canvas states before shop:", canvasStateA, canvasStateB);

    // Save current image-uploader pairs to localStorage before navigating
    if (combinedImageUploaderPairs.length > 0) {
      saveImageUploaderPairs(combinedImageUploaderPairs);
      console.log(
        "Saved image-uploader pairs to localStorage before navigating to shop"
      );
    }

    if (testCanvas) {
      // Save current canvas state
      const currentState = testCanvas.toJSON([
        "id",
        "selectable",
        "left",
        "top",
        "scaleX",
        "scaleY",
        "angle",
        "flipX",
        "flipY",
        "imageId",
      ]);

      // Update the appropriate canvas state based on current flip state
      if (flipState) {
        // We're on the back side, update canvasStateB
        setCanvasStateB(currentState);
      } else {
        // We're on the front side, update canvasStateA
        setCanvasStateA(currentState);
      }

      // Save both canvas states to session storage using our utility
      // Use the updated state for the current side
      const stateToSaveA = !flipState ? currentState : canvasStateA;
      const stateToSaveB = flipState ? currentState : canvasStateB;

      try {
        // Use our safe storage utility instead of direct sessionStorage
        saveCanvasState("canvasStateA", stateToSaveA);
        saveCanvasState("canvasStateB", stateToSaveB);
        saveCanvasState("canvasState", currentState);

        console.log("Saved canvas states to session storage");
      } catch (error) {
        console.error("Error saving canvas states:", error);
      }
    }

    navigate("/shop", {
      state: {
        fromProduct: true,
        fromShopOrFavorites: true, // Add this flag to identify when returning from shop
        productId: id,
        product: product,
        tshirtFacing: flipState, // Pass the current tshirt facing state (front=false, back=true)
      },
    });
  };

  const handleProductChange = (newProduct) => {
    console.log(newProduct);
    // Save current canvas state
    if (testCanvas) {
      const currentState = JSON.stringify(testCanvas);
      sessionStorage.setItem("canvasState", currentState);
    }

    navigate(`/products-details/${newProduct._id}`, {
      state: {
        selectedImageUrl: null, // No new image being added
        product: newProduct,
      },
    });

    setShowProductSelector(false);
  };

  const handleAddFromFavorites = () => {
    console.log("Canvas states before favorites:", canvasStateA, canvasStateB);
    console.log("Current flip state (back):", flipState);

    // Save current image-uploader pairs to localStorage before navigating
    if (combinedImageUploaderPairs.length > 0) {
      saveImageUploaderPairs(combinedImageUploaderPairs);
      console.log(
        "Saved image-uploader pairs to localStorage before navigating to favorites"
      );
    }

    if (testCanvas) {
      // Save current canvas state
      const currentState = testCanvas.toJSON([
        "id",
        "selectable",
        "left",
        "top",
        "scaleX",
        "scaleY",
        "angle",
        "flipX",
        "flipY",
        "imageId",
      ]);

      console.log("Current canvas state:", currentState);

      // Update the appropriate canvas state based on current flip state
      if (flipState) {
        // We're on the back side, update canvasStateB
        console.log("Updating canvasStateB with current state");
        setCanvasStateB(currentState);
      } else {
        // We're on the front side, update canvasStateA
        console.log("Updating canvasStateA with current state");
        setCanvasStateA(currentState);
      }

      // Save both canvas states to session storage
      // Use the updated state for the current side
      const stateToSaveA = !flipState ? currentState : canvasStateA;
      const stateToSaveB = flipState ? currentState : canvasStateB;

      console.log("State to save for A:", stateToSaveA);
      console.log("State to save for B:", stateToSaveB);

      try {
        // Use our safe storage utility instead of direct sessionStorage
        saveCanvasState("canvasStateA", stateToSaveA);
        saveCanvasState("canvasStateB", stateToSaveB);
        saveCanvasState("canvasState", currentState);

        console.log("Saved canvas states to session storage");
      } catch (error) {
        console.error("Error saving canvas states:", error);
      }
    }

    navigate("/favorites", {
      state: {
        fromProduct: true,
        fromShopOrFavorites: true, // Add this flag to identify when returning from favorites
        productId: id,
        product: product,
        tshirtFacing: flipState, // Pass the current tshirt facing state (front=false, back=true)
      },
    });
  };

  // Function to duplicate the selected object
  const handleDuplicate = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (!activeObject) return;

    // Clone the object
    activeObject.clone((cloned) => {
      // Position the cloned object slightly offset from the original
      cloned.set({
        left: cloned.left + 10,
        top: cloned.top + 10,
      });

      // Add the cloned object to the canvas
      testCanvas.add(cloned);

      // Select the cloned object
      testCanvas.setActiveObject(cloned);

      // Update the addedObject state
      setAddedObject(testCanvas.getObjects());

      // Render the canvas
      testCanvas.renderAll();

      // Save state to undo stack
      const json = testCanvas.toJSON();
      setUndoStack((prev) => {
        if (
          prev.length > 0 &&
          JSON.stringify(prev[prev.length - 1]) === JSON.stringify(json)
        ) {
          return prev;
        }
        return [...prev, json];
      });
      setRedoStack([]); // Clear redo stack on new action
    });
  };

  // Function to flip the selected object horizontally
  const handleFlipHorizontally = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (!activeObject) return;

    // Flip the object horizontally
    activeObject.set("flipX", !activeObject.flipX);

    // Render the canvas
    testCanvas.renderAll();

    // Save state to undo stack
    const json = testCanvas.toJSON();
    setUndoStack((prev) => {
      if (
        prev.length > 0 &&
        JSON.stringify(prev[prev.length - 1]) === JSON.stringify(json)
      ) {
        return prev;
      }
      return [...prev, json];
    });
    setRedoStack([]); // Clear redo stack on new action
  };

  // Function to flip the selected object vertically
  const handleFlipVertically = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (!activeObject) return;

    // Flip the object vertically
    activeObject.set("flipY", !activeObject.flipY);

    // Render the canvas
    testCanvas.renderAll();

    // Save state to undo stack
    const json = testCanvas.toJSON();
    setUndoStack((prev) => {
      if (
        prev.length > 0 &&
        JSON.stringify(prev[prev.length - 1]) === JSON.stringify(json)
      ) {
        return prev;
      }
      return [...prev, json];
    });
    setRedoStack([]); // Clear redo stack on new action
  };

  useEffect(() => {
    const handleKeyDown = (e) => {
      // Skip if we're in an input field or textarea
      if (e.target.tagName === "INPUT" || e.target.tagName === "TEXTAREA") {
        return;
      }

      if (e.ctrlKey || e.metaKey) {
        // Support for both Windows/Linux and Mac
        switch (e.key.toLowerCase()) {
          case "z":
            e.preventDefault();
            handleUndo();
            break;
          case "y":
            e.preventDefault();
            handleRedo();
            break;
          case "d":
            e.preventDefault();
            handleDuplicate();
            break;
          default:
            break;
        }
      } else if (e.key === "Delete" || e.key === "Backspace") {
        // Only prevent default for Delete key to avoid interfering with browser back navigation
        if (e.key === "Delete") {
          e.preventDefault();
        }

        // Only handle delete if we have a canvas and an active object
        if (testCanvas) {
          const activeObject = testCanvas.getActiveObject();
          if (activeObject) {
            // If it's a group of objects
            if (activeObject.type === "activeSelection") {
              // Get all objects in the selection
              const activeObjects = activeObject.getObjects();

              // Remove each object in the selection
              activeObjects.forEach((obj) => {
                // If it's an image with an imageId, remove it from the imageIds state
                if (obj.type === "image" && obj.imageId) {
                  setImageIds((prevIds) =>
                    prevIds.filter((id) => id !== obj.imageId)
                  );
                }
                testCanvas.remove(obj);
              });

              // Discard the active selection
              testCanvas.discardActiveObject();
            } else {
              // If it's a single object
              handleDeleteObject(activeObject);
            }

            // Update the addedObject state
            setAddedObject(testCanvas.getObjects());

            // Render the canvas
            testCanvas.renderAll();

            // Save state to undo stack
            const json = testCanvas.toJSON();
            setUndoStack((prev) => {
              if (
                prev.length > 0 &&
                JSON.stringify(prev[prev.length - 1]) === JSON.stringify(json)
              ) {
                return prev;
              }
              return [...prev, json];
            });
            setRedoStack([]); // Clear redo stack on new action
          }
        }
      } else {
        // Single key shortcuts (without modifiers)
        switch (e.key) {
          case "h":
            if (!e.shiftKey && !e.altKey && !e.ctrlKey && !e.metaKey) {
              e.preventDefault();
              handleFlipHorizontally();
            }
            break;
          case "v":
            if (!e.shiftKey && !e.altKey && !e.ctrlKey && !e.metaKey) {
              e.preventDefault();
              handleFlipVertically();
            }
            break;
          case "f":
            if (!e.shiftKey && !e.altKey && !e.ctrlKey && !e.metaKey) {
              e.preventDefault();
              // Only flip if the product has a back image
              if (product?.imageBack) {
                handleFlipClick();
              }
            }
            break;
          default:
            break;
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [
    handleUndo,
    handleRedo,
    handleDuplicate,
    handleFlipClick,
    testCanvas,
    product,
  ]);

  // When product loads, set default white color
  useEffect(() => {
    if (product?.color?.length > 0) {
      const whiteColor = product.color.find(
        (color) =>
          color.hex_code?.toLowerCase() === "#ffffff" ||
          color.hex_code?.toLowerCase() === "#fff"
      );
      if (whiteColor) {
        setSelectedColors([whiteColor._id]);
      }
    }
  }, [product]);

  // Remove 'fromCart' from localStorage when on product details page
  useEffect(() => {
    if (localStorage.getItem("fromCart")) {
      localStorage.removeItem("fromCart");
    }
  }, []);

  return (
    <div
      ref={containerRef}
      className="min-h-screen bg-gray-50 dark:bg-gray-900 w-full"
    >
      <CanvasInitializer
        setCanvas={setCanvas}
        drawHeight={drawHeight}
        drawWidth={drawWidth}
        drawWidthInches={drawWidthInches}
        drawHeightInches={drawHeightInches}
        dpi={dpi}
        enlargedScale={enlargedScale}
        setUndoStack={setUndoStack}
        setRedoStack={setRedoStack}
        setSelectedObject={setSelectedObject}
      />

      <div
        className={`flex flex-col md:flex-row w-full min-h-[calc(100vh-56px)]" ${
          viewPreview
            ? "items-center justify-center"
            : "items-start justify-center"
        } relative`}
      >
        {/* Left Side - Tools Panel (Desktop/Tablet Only) */}
        {!viewPreview && !isMobile && (
          <div className="md:h-[calc(100vh-56px)] shadow-sm flex-shrink-0 flex md:sticky top-0 overflow-x-auto">
            <SidePanel
              activeComponent={activeComponent}
              toggleComponent={toggleComponent}
              handleFileUp={handleFileUp}
              handleAddFromShop={handleAddFromShop}
              handleAddFromFavorites={handleAddFromFavorites}
              testCanvas={testCanvas}
              addObject={addObject}
              displayShapes={displayShapes}
              setDisplayShapes={setDisplayShapes}
              selectedFontColor={selectedFontColor}
              setSelectedFontColor={setSelectedFontColor}
              selectedFontFamily={selectedFontFamily}
              setSelectedFontFamily={setSelectedFontFamily}
              addedObjects={addedObject}
              handleObjectSelection={handleObjectSelection}
              handleDeleteObject={handleDeleteObject}
              handleMoveLayerUp={handleMoveLayerUp}
              handleMoveLayerDown={handleMoveLayerDown}
              handleBringToFront={handleBringToFront}
              handleSendToBack={handleSendToBack}
              handleReorderLayers={handleReorderLayers}
              fromAffiliate={fromAffiliate}
              isRemovingBackground={isRemovingBackground}
              setIsRemovingBackground={setIsRemovingBackground}
              setAddedObject={setAddedObject}
            />
          </div>
        )}

        {/* Floating Action Button Container - Fixed at bottom right (Desktop/Tablet Only) */}
        {!isMobile && (
          <div className="fixed bottom-6 right-6 z-50">
            <FloatingActionButton
              testCanvas={testCanvas}
              product={product}
              canvasStateA={canvasStateA}
              canvasStateB={canvasStateB}
              drawWidth={drawWidth}
              drawHeight={drawHeight}
              setShowProductSelector={setShowProductSelector}
              setViewPreview={setViewPreview}
              viewPreview={viewPreview}
              selectedColors={selectedColors}
              setModalVisible={setModalVisible}
              setCheckoutData={setCheckoutData}
              checkoutData={checkoutData}
              fromAffiliate={fromAffiliate}
              flipState={flipState}
              combinedImageIds={combinedImageIds}
              updateCombinedImageIds={updateCombinedImageIds}
              imageUploaderPairs={combinedImageUploaderPairs}
              updateImageUploaderPairs={updateCombinedImageUploaderPairs}
            />
          </div>
        )}

        {/* Mobile Floating Action Button (Mobile Only) */}
        {isMobile && (
          <MobileFloatingActionButton
            testCanvas={testCanvas}
            product={product}
            canvasStateA={canvasStateA}
            canvasStateB={canvasStateB}
            drawWidth={drawWidth}
            drawHeight={drawHeight}
            setShowProductSelector={setShowProductSelector}
            setViewPreview={setViewPreview}
            viewPreview={viewPreview}
            selectedColors={selectedColors}
            setModalVisible={setModalVisible}
            setCheckoutData={setCheckoutData}
            checkoutData={checkoutData}
            fromAffiliate={fromAffiliate}
            flipState={flipState}
            combinedImageIds={combinedImageIds}
            updateCombinedImageIds={updateCombinedImageIds}
            imageUploaderPairs={combinedImageUploaderPairs}
            updateImageUploaderPairs={updateCombinedImageUploaderPairs}
            onOpenSidePanel={() => setShowMobileSidePanel(true)}
            onOpenToolbar={() => setShowMobileToolbar(true)}
            setAddedObject={setAddedObject}
            undoStack={undoStack}
            setUndoStack={setUndoStack}
            redoStack={redoStack}
            setRedoStack={setRedoStack}
          />
        )}

        {/* Right Side - Canvas Area */}
        <div
          className={`${
            viewPreview ? "w-full max-w-4xl mx-auto" : "flex-1 max-w-4xl"
          } bg-white dark:bg-gray-800 flex flex-col min-w-0 transition-colors duration-200 mx-auto`}
        >
          {/* Toolbar - Full width at top (Desktop/Tablet Only) */}
          {!viewPreview && !isMobile && (
            <div className="mb-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm dark:shadow-gray-900 p-2 transition-colors duration-200 sticky top-0 z-10 w-full left-0 right-0 overflow-x-auto">
              <ToolBar
                testCanvas={testCanvas}
                undoStack={undoStack}
                setUndoStack={setUndoStack}
                redoStack={redoStack}
                setRedoStack={setRedoStack}
                flipState={flipState}
                setAddedObject={setAddedObject}
                handleFlipClick={handleFlipClick}
                setSelectedImage={setSelectedImage}
                setIsEnhancing={setIsEnhancing}
              />
            </div>
          )}

          {/* Canvas Container */}
          <div className="flex-grow bg-gray-50 dark:bg-gray-900 rounded-lg p-2 sm:p-3 md:p-4 flex items-center justify-center transition-colors duration-200 min-h-[350px] sm:min-h-[450px] md:min-h-[550px] lg:min-h-[630px]">
            <div
              className={`shadow-lg dark:shadow-gray-900 bg-white dark:bg-gray-800 transition-colors duration-200 w-full h-full flex items-center justify-center`}
              style={{
                aspectRatio: "3/4",
                maxHeight: isMobile
                  ? "calc(100vh - 150px)"
                  : "calc(100vh - 200px)",
                maxWidth: "800px",
                width: "auto",
                margin: "0 auto",
              }}
            >
              <CanvasArea
                product={product}
                flipState={flipState}
                drawWidth={drawWidth}
                drawHeight={drawHeight}
                viewPreview={viewPreview}
                selectedObject={selectedObject}
                canvas={testCanvas}
                handleFlipClick={isMobile ? handleFlipClick : null}
              />
            </div>
          </div>

          {/* Mobile Quick Tools - Only shown on mobile */}
          {isMobile && !viewPreview && (
            <MobileQuickTools
              testCanvas={testCanvas}
              undoStack={undoStack}
              setUndoStack={setUndoStack}
              redoStack={redoStack}
              setRedoStack={setRedoStack}
              setAddedObject={setAddedObject}
              handleDeleteObject={handleDeleteObject}
              setSelectedImage={setSelectedImage}
            />
          )}

          {/* Color Picker */}
          {!viewPreview && (
            <div
              className={` bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm dark:shadow-gray-900 p-4 transition-colors duration-200 ${
                isMobile ? "mb-16" : ""
              }`}
            >
              {/* <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center transition-colors duration-200">
                <span className="w-4 h-4 bg-teal-500 dark:bg-teal-400 rounded-full mr-2 transition-colors duration-200"></span>
                Product Colors
              </h3> */}
              <ColorPickerComponent
                availableColors={product?.color}
                selectedColors={selectedColors}
                setSelectedColors={setSelectedColors}
              />
            </div>
          )}
        </div>

        {/* Modals */}
        <ProductSelector
          products={products}
          showModal={showProductSelector}
          onSelect={handleProductChange}
          onClose={() => setShowProductSelector(false)}
        />

        <CheckoutModal
          isVisible={isModalVisible}
          onClose={() => setModalVisible(false)}
          productDetails={product}
          checkoutData={checkoutData}
          fromAffiliate={fromAffiliate}
        />
      </div>

      {/* Mobile-specific modals */}
      {isMobile && (
        <>
          {/* Mobile Side Panel */}
          <MobileSidePanel
            activeComponent={activeComponent}
            toggleComponent={toggleComponent}
            handleFileUp={handleFileUp}
            handleAddFromShop={handleAddFromShop}
            handleAddFromFavorites={handleAddFromFavorites}
            testCanvas={testCanvas}
            addObject={addObject}
            displayShapes={displayShapes}
            setDisplayShapes={setDisplayShapes}
            selectedFontColor={selectedFontColor}
            setSelectedFontColor={setSelectedFontColor}
            selectedFontFamily={selectedFontFamily}
            setSelectedFontFamily={setSelectedFontFamily}
            addedObjects={addedObject}
            handleObjectSelection={handleObjectSelection}
            handleDeleteObject={handleDeleteObject}
            handleMoveLayerUp={handleMoveLayerUp}
            handleMoveLayerDown={handleMoveLayerDown}
            handleBringToFront={handleBringToFront}
            handleSendToBack={handleSendToBack}
            handleReorderLayers={handleReorderLayers}
            fromAffiliate={fromAffiliate}
            isRemovingBackground={isRemovingBackground}
            setIsRemovingBackground={setIsRemovingBackground}
            setAddedObject={setAddedObject}
            isMobile={isMobile}
            isOpen={showMobileSidePanel}
            onClose={() => setShowMobileSidePanel(false)}
          />

          {/* Mobile Toolbar */}
          <MobileToolBar
            testCanvas={testCanvas}
            undoStack={undoStack}
            setUndoStack={setUndoStack}
            redoStack={redoStack}
            setRedoStack={setRedoStack}
            setAddedObject={setAddedObject}
            setSelectedImage={setSelectedImage}
            isOpen={showMobileToolbar}
            onClose={() => setShowMobileToolbar(false)}
          />
        </>
      )}

      {/* Image Enhancement Tool */}
      <ImageEnhancementTool
        canvas={testCanvas}
        isEnhancing={isEnhancing}
        setIsEnhancing={setIsEnhancing}
        setAddedObject={setAddedObject}
      />
    </div>
  );
};

export default Prod;
